# سجل التغييرات - تقارير الإجازات المتقدمة

## [1.2.0] - 2025-01-10

### 🔧 إصلاحات الأخطاء
- إصلاح مشكلة `AttributeError: 'hr.employee' object has no attribute 'registration_number'`
- تحسين منطق الحصول على الرقم الوظيفي من عدة مصادر محتملة (int_id، barcode، identification_id)
- إضافة معالجة أخطاء شاملة للحقول غير الموجودة

### 🎉 ميزات جديدة
- **تقرير رصيد الإجازات المتاح**: ميزة جديدة كاملة لعرض أرصدة الإجازات
- **تصدير Excel**: إمكانية تصدير تقرير رصيد الإجازات بصيغة Excel مع تنسيق احترافي
- **تصفية متقدمة**: فلترة حسب الموظف، القسم، ونوع الإجازة
- **إحصائيات شاملة**: عرض إجماليات الأرصدة والاستخدام
- **واجهة عربية**: تصميم كامل باللغة العربية مع دعم RTL

### 📊 البيانات المعروضة الجديدة
- الرقم الوظيفي للموظف
- اسم الموظف والقسم
- نوع الإجازة
- إجمالي الأيام المخصصة
- الأيام المستخدمة
- الرصيد المتاح

### 🔧 تحسينات تقنية
- إضافة مكتبة xlsxwriter كتبعية اختيارية
- تحسين هيكل الكود وإضافة معالجة أخطاء شاملة
- إضافة دليل استخدام مفصل للميزة الجديدة
- إضافة ملفات اختبار شاملة

## [1.1.3] - 2024-12-19

### 🔧 إصلاحات الأخطاء
- إصلاح مشكلة `UnboundLocalError: local variable '_' referenced before assignment`
- استبدال استخدام دالة الترجمة `_()` بنصوص ثابتة لتجنب مشاكل السياق
- تحسين استقرار النظام

## [1.1.2] - 2024-12-19

### 🔧 إصلاحات الأخطاء
- إضافة حقل `int_id` كحل احتياطي لتجنب تعارض مع مديولات أخرى
- ضمان التوافق مع مديول `hr_employees_masarat`

## [1.1.1] - 2024-12-19

### 🔧 إصلاحات الأخطاء
- إصلاح مرجع XML في `hr_leave_type_views.xml` من `hr_holidays.hr_leave_type_view_form` إلى `hr_holidays.edit_holiday_status_form`
- حل مشكلة ParseError في ملف الواجهات

## [1.1.0] - 2024-12-19

### ✨ مميزات جديدة
- **الطباعة التلقائية عند الموافقة**: إنشاء تقرير PDF تلقائياً عند الموافقة على الإجازة
- **إرسال التقرير للموظف**: إرسال التقرير عبر البريد الإلكتروني للموظف مع رسالة تأكيد
- **حفظ كمرفق**: حفظ التقرير كمرفق في سجل الإجازة للرجوع إليه لاحقاً
- **تحكم في التفعيل**: إمكانية تفعيل/إلغاء تفعيل الميزة لكل نوع إجازة على حدة

### 🔧 تحسينات تقنية
- إضافة حقل `auto_generate_report` في نموذج `hr.leave.type`
- توسيع نموذج `hr.leave` لدعم الطباعة التلقائية
- معالجة الأخطاء بشكل آمن دون إيقاف عملية الموافقة
- إضافة اختبارات شاملة للوظيفة الجديدة

### 📝 تحديثات التوثيق
- إضافة دليل استخدام الطباعة التلقائية
- تحديث ملف README بالمميزات الجديدة
- إضافة ترجمات عربية للنصوص الجديدة

## [1.0.1] - 2024-12-19

### 🔧 إصلاحات الأخطاء
- إصلاح مشكلة `AttributeError: 'hr.employee' object has no attribute 'employee_id'`
- إصلاح مشكلة `'str' object has no attribute 'strftime'` في قالب التقرير
- تحسين معالجة رقم الموظف باستخدام `identification_id` أو `barcode` أو `id`
- تحسين تنسيق تاريخ التقرير

### 📝 تحديثات
- تحديث ملفات الاختبارات لتتوافق مع الإصلاحات
- تحديث التوثيق والأدلة

## [1.0.0] - 2024-12-19

### ✨ المميزات الجديدة
- إنشاء مديول تقارير الإجازات المتقدمة كمديول منفصل
- تقرير شامل للإجازات بصيغة PDF مع تصميم احترافي
- دعم كامل للغة العربية مع اتجاه النص من اليمين إلى اليسار
- تصفية متقدمة حسب:
  - الفترة الزمنية (من تاريخ - إلى تاريخ)
  - الموظف (موظف محدد أو كل الموظفين)
  - حالة الإجازة (كل الحالات أو حالة محددة)

### 📊 البيانات المعروضة
- رقم الموظف الوظيفي
- اسم الموظف
- القسم
- نوع الإجازة
- تاريخ بداية ونهاية الإجازة
- عدد الأيام
- حالة الإجازة
- تاريخ تقديم الطلب

### 📈 الإحصائيات
- إجمالي عدد الإجازات
- إجمالي عدد الأيام
- متوسط أيام الإجازة

### 🎨 التصميم
- تصميم احترافي ومريح للعين
- ألوان متناسقة ومناسبة للطباعة
- تخطيط منظم وسهل القراءة
- رأس وتذييل مميز للتقرير

### 🔧 التقنيات
- نموذج Wizard لإعداد التقرير
- قالب QWeb للتقرير
- نظام صلاحيات متكامل
- اختبارات وحدة شاملة

### 📱 واجهة المستخدم
- نموذج سهل الاستخدام لإعداد التقرير
- رسائل تحقق من صحة البيانات
- إرشادات واضحة للمستخدم

### 🔒 الأمان
- صلاحيات مناسبة لمستخدمي ومديري الإجازات
- التحقق من صحة البيانات المدخلة
- حماية من الأخطاء الشائعة

### 📋 المتطلبات
- Odoo 15.0 أو أحدث
- مديول hr_holidays

### 🧪 الاختبارات
- اختبارات إنشاء الـ wizard
- اختبارات جمع البيانات
- اختبارات التحقق من التواريخ
- اختبارات تصفية الموظفين
- اختبارات إنشاء التقرير

---

## خطط المستقبل

### الإصدار 1.1.0 (مخطط)
- إضافة تصدير Excel
- تقارير إضافية (تقرير حسب القسم، تقرير شهري)
- رسوم بيانية وإحصائيات متقدمة

### الإصدار 1.2.0 (مخطط)
- تقارير تفاعلية
- إشعارات تلقائية
- تكامل مع البريد الإلكتروني

---

**ملاحظة**: هذا المديول في تطوير مستمر ونرحب بالاقتراحات والتحسينات.
