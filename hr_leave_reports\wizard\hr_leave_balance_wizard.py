# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import json
import io
import base64
try:
    import xlsxwriter
except ImportError:
    xlsxwriter = None


class HrLeaveBalanceWizard(models.TransientModel):
    _name = 'hr.leave.balance.wizard'
    _description = 'تقرير رصيد الإجازات'

    employee_id = fields.Many2one(
        'hr.employee',
        string='الموظف'
    )
    department_id = fields.Many2one(
        'hr.department',
        string='القسم'
    )
    all_employees = fields.Boolean(
        string='جميع الموظفين',
        default=True
    )
    leave_type_id = fields.Many2one(
        'hr.leave.type',
        string='نوع الإجازة'
    )
    all_leave_types = fields.Boolean(
        string='جميع أنواع الإجازات',
        default=True
    )
    active_employees_only = fields.Boolean(
        string='الموظفين النشطين فقط',
        default=True
    )

    @api.onchange('all_employees')
    def _onchange_all_employees(self):
        if self.all_employees:
            self.employee_id = False
            self.department_id = False

    @api.onchange('all_leave_types')
    def _onchange_all_leave_types(self):
        if self.all_leave_types:
            self.leave_type_id = False

    @api.onchange('department_id')
    def _onchange_department_id(self):
        if self.department_id:
            self.all_employees = False

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        if self.employee_id:
            self.all_employees = False
            self.department_id = False

    def _get_employees_domain(self):
        """إنشاء domain للموظفين حسب الفلاتر المحددة"""
        domain = []
        
        if self.active_employees_only:
            domain.append(('active', '=', True))
        
        if not self.all_employees:
            if self.employee_id:
                domain.append(('id', '=', self.employee_id.id))
            elif self.department_id:
                domain.append(('department_id', '=', self.department_id.id))
        
        return domain

    def _get_leave_types_domain(self):
        """إنشاء domain لأنواع الإجازات"""
        domain = [('active', '=', True)]
        
        if not self.all_leave_types and self.leave_type_id:
            domain.append(('id', '=', self.leave_type_id.id))
        
        return domain

    def _get_employee_leave_balance_data(self, employee, leave_type):
        """حساب رصيد الإجازات للموظف ونوع الإجازة المحدد"""
        # الحصول على بيانات الإجازات للموظف
        employee_days = leave_type.with_context(employee_id=employee.id).get_employees_days([employee.id])
        employee_data = employee_days.get(employee.id, {})
        leave_type_data = employee_data.get(leave_type.id, {})
        
        # الحصول على الرقم الوظيفي من عدة مصادر محتملة
        employee_number = ''
        if hasattr(employee, 'int_id') and employee.int_id:
            employee_number = employee.int_id
        elif hasattr(employee, 'barcode') and employee.barcode:
            employee_number = employee.barcode
        elif hasattr(employee, 'identification_id') and employee.identification_id:
            employee_number = employee.identification_id
        else:
            employee_number = str(employee.id)

        return {
            'employee_id': employee.id,
            'employee_name': employee.name,
            'employee_number': employee_number,
            'department': employee.department_id.name if employee.department_id else '',
            'leave_type': leave_type.name,
            'max_leaves': leave_type_data.get('max_leaves', 0),
            'leaves_taken': leave_type_data.get('leaves_taken', 0),
            'remaining_leaves': leave_type_data.get('remaining_leaves', 0),
            'virtual_remaining_leaves': leave_type_data.get('virtual_remaining_leaves', 0),
        }

    def _get_report_data(self):
        """جمع بيانات التقرير"""
        employees = self.env['hr.employee'].search(self._get_employees_domain())
        leave_types = self.env['hr.leave.type'].search(self._get_leave_types_domain())
        
        data = []
        total_max_leaves = 0
        total_taken_leaves = 0
        total_remaining_leaves = 0
        
        for employee in employees:
            for leave_type in leave_types:
                balance_data = self._get_employee_leave_balance_data(employee, leave_type)
                
                # تجاهل الأنواع التي لا تحتاج تخصيص أو ليس لها رصيد
                if leave_type.requires_allocation == 'yes' and balance_data['max_leaves'] > 0:
                    data.append(balance_data)
                    total_max_leaves += balance_data['max_leaves']
                    total_taken_leaves += balance_data['leaves_taken']
                    total_remaining_leaves += balance_data['remaining_leaves']
        
        return {
            'data': data,
            'totals': {
                'total_max_leaves': total_max_leaves,
                'total_taken_leaves': total_taken_leaves,
                'total_remaining_leaves': total_remaining_leaves,
                'total_records': len(data)
            },
            'filters': {
                'employee_name': self.employee_id.name if self.employee_id else 'جميع الموظفين',
                'department_name': self.department_id.name if self.department_id else 'جميع الأقسام',
                'leave_type_name': self.leave_type_id.name if self.leave_type_id else 'جميع أنواع الإجازات',
                'active_only': self.active_employees_only
            }
        }

    def action_print_pdf_report(self):
        """طباعة التقرير بصيغة PDF"""
        data = self._get_report_data()
        
        if not data['data']:
            raise ValidationError('لا توجد بيانات لعرضها في التقرير')
        
        return self.env.ref('hr_leave_reports.hr_leave_balance_report_pdf').report_action(self, data=data)

    def action_export_excel(self):
        """تصدير التقرير بصيغة Excel"""
        if not xlsxwriter:
            raise ValidationError('مكتبة xlsxwriter غير مثبتة. يرجى تثبيتها لتصدير ملفات Excel')
        
        data = self._get_report_data()
        
        if not data['data']:
            raise ValidationError('لا توجد بيانات لتصديرها')
        
        # إنشاء ملف Excel
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        worksheet = workbook.add_worksheet('تقرير رصيد الإجازات')
        
        # تنسيقات الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4CAF50',
            'font_color': 'white',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        
        cell_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        
        number_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'num_format': '0.00'
        })
        
        # عناوين الأعمدة
        headers = [
            'الرقم الوظيفي',
            'اسم الموظف',
            'القسم',
            'نوع الإجازة',
            'إجمالي الأيام المخصصة',
            'الأيام المستخدمة',
            'الرصيد المتاح'
        ]
        
        # كتابة العناوين
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)
        
        # كتابة البيانات
        for row, record in enumerate(data['data'], start=1):
            worksheet.write(row, 0, record['employee_number'], cell_format)
            worksheet.write(row, 1, record['employee_name'], cell_format)
            worksheet.write(row, 2, record['department'], cell_format)
            worksheet.write(row, 3, record['leave_type'], cell_format)
            worksheet.write(row, 4, record['max_leaves'], number_format)
            worksheet.write(row, 5, record['leaves_taken'], number_format)
            worksheet.write(row, 6, record['remaining_leaves'], number_format)
        
        # إضافة صف الإجماليات
        total_row = len(data['data']) + 2
        worksheet.write(total_row, 0, 'الإجماليات', header_format)
        worksheet.write(total_row, 4, data['totals']['total_max_leaves'], number_format)
        worksheet.write(total_row, 5, data['totals']['total_taken_leaves'], number_format)
        worksheet.write(total_row, 6, data['totals']['total_remaining_leaves'], number_format)
        
        # تعديل عرض الأعمدة
        worksheet.set_column('A:A', 15)  # الرقم الوظيفي
        worksheet.set_column('B:B', 25)  # اسم الموظف
        worksheet.set_column('C:C', 20)  # القسم
        worksheet.set_column('D:D', 20)  # نوع الإجازة
        worksheet.set_column('E:G', 15)  # الأرقام
        
        workbook.close()
        output.seek(0)
        
        # إنشاء attachment
        filename = f'تقرير_رصيد_الإجازات_{fields.Date.today().strftime("%Y-%m-%d")}.xlsx'
        attachment = self.env['ir.attachment'].create({
            'name': filename,
            'type': 'binary',
            'datas': base64.b64encode(output.read()),
            'store_fname': filename,
            'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'self',
        }
